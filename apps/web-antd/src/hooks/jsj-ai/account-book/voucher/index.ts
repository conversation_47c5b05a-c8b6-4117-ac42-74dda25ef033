/**
 * 摘要选择逻辑
 */
import type { AuxiliaryItm, SummaryItmData } from '#/api/account-book/bookkeeping';
import type {
  AccountSubjectItem,
  AssistantAccountingItem,
  AccountSubjectQueryParams,
  AssistantAccountingQueryParams
} from '#/api/jsj-ai/types';

import { computed, onMounted, ref, watch } from 'vue';

import {
  fetchAccountSubjectList,
  fetchAssistantAccountingList
} from '#/api/jsj-ai/api-v2';
import { useCompanySelection } from '../../ai-chat/useCompanySelection';

// 全局缓存
const accountSubjectsCache = new Map<string, AccountSubjectItem[]>();
const assistantAccountingCache = new Map<string, Record<string, AssistantAccountingItem[]>>();
const cacheExpiry = new Map<string, number>();
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存


// 会计科目选择器数据接口
export interface AccountSubjectOption {
  value: string; // 科目ID
  label: string; // 显示文本：科目代码 + 科目名称 + 辅助核算信息
  code: string; // 科目代码
  name: string; // 科目名称
  useAssistant: boolean; // 是否启用辅助核算
  assistantType?: string; // 辅助核算类型
  assistantOptions?: AssistantAccountingItem[]; // 辅助核算选项
}

// 辅助核算类型映射
const ASSISTANT_TYPE_MAP = {
  s: 'supplier',    // 供应商
  c: 'customer',    // 客户
  i: 'inventory',   // 存货
  p: 'project',     // 项目
  d: 'department',  // 部门
  e: 'employee',    // 员工
} as const;

/**
 * 会计科目选择器 hooks
 * 提供格式化的会计科目选项，包含辅助核算信息
 */
export const useAccountSubjects = () => {
  // 安全地获取公司选择
  let selectedCompany: any;
  try {
    const companySelection = useCompanySelection();
    selectedCompany = companySelection.selectedCompany;
  } catch (error) {
    console.error('获取公司选择失败:', error);
    // 创建一个默认的 ref
    selectedCompany = ref('');
  }

  // 原始数据
  const accountSubjects = ref<AccountSubjectItem[]>([]);
  const assistantAccounting = ref<Record<string, AssistantAccountingItem[]>>({});
  const loading = ref(false);
  const error = ref<string | null>(null);

  /**
   * 递归提取所有最底层科目（叶子节点）
   */
  const extractLeafSubjects = (subjects: AccountSubjectItem[]): AccountSubjectItem[] => {
    const leafSubjects: AccountSubjectItem[] = [];

    const traverse = (items: AccountSubjectItem[]) => {
      for (const item of items) {
        if (item.children && item.children.length > 0) {
          // 有子节点，继续递归
          traverse(item.children);
        } else {
          // 叶子节点，添加到结果中
          leafSubjects.push(item);
        }
      }
    };

    traverse(subjects);
    return leafSubjects;
  };

  /**
   * 格式化科目显示文本
   */
  const formatSubjectLabel = (
    subject: AccountSubjectItem,
    assistantItem?: AssistantAccountingItem
  ): string => {
    let label = `${subject.code} ${subject.fullName}`;

    if (assistantItem) {
      label += ` ${assistantItem.code} ${assistantItem.name}`;
    }

    return label;
  };

  /**
   * 获取辅助核算选项
   */
  const getAssistantOptions = (assistantType: string): AssistantAccountingItem[] => {
    if (!assistantType || !assistantAccounting.value) {
      console.log('⚠️ getAssistantOptions: 缺少参数或数据', {
        assistantType,
        hasAssistantAccounting: !!assistantAccounting.value,
      });
      return [];
    }

    // 首先尝试直接使用 assistantType（如果已经是完整的类型名）
    let targetType = assistantType;

    // 如果是单字符，则进行映射转换
    if (assistantType.length === 1) {
      const mappedType = ASSISTANT_TYPE_MAP[assistantType as keyof typeof ASSISTANT_TYPE_MAP];
      if (mappedType) {
        targetType = mappedType;
      }
    }

    console.log('🔧 getAssistantOptions: 类型映射', {
      originalType: assistantType,
      targetType,
      availableTypes: Object.keys(assistantAccounting.value),
      hasTargetType: !!assistantAccounting.value[targetType],
    });

    const options = assistantAccounting.value[targetType] || [];
    console.log('📊 getAssistantOptions: 获取结果', {
      targetType,
      optionsCount: options.length,
      firstFewOptions: options.slice(0, 3).map(opt => ({ id: opt.id, name: opt.name, code: opt.code })),
    });

    return options;
  };

  /**
   * 生成科目选项列表（包含辅助核算）
   */
  const subjectOptions = computed<AccountSubjectOption[]>(() => {
    const allSubjects = extractLeafSubjects(accountSubjects.value);
    const options: AccountSubjectOption[] = [];

    for (const subject of allSubjects) {
      if (subject.useAssistant && subject.assistantType) {
        // 启用辅助核算的科目
        const assistantOptions = getAssistantOptions(subject.assistantType);

        if (assistantOptions.length > 0) {
          // 为每个辅助核算项创建一个选项
          for (const assistantItem of assistantOptions) {
            // 新增科目的ID为null，使用code；现有科目使用ID
            const subjectIdentifier = subject.id ? subject.id.toString() : subject.code;
            const assistantIdentifier = assistantItem.id ? assistantItem.id.toString() : assistantItem.code;

            options.push({
              value: `${subjectIdentifier}_${assistantIdentifier}`, // 组合ID
              label: formatSubjectLabel(subject, assistantItem),
              code: subject.code,
              name: subject.fullName,
              useAssistant: true,
              assistantType: subject.assistantType,
              assistantOptions: [assistantItem],
            });
          }
        } else {
          // 辅助核算选项为空，只显示科目本身
          options.push({
            // 新增科目的ID为null，使用code作为value；现有科目使用ID
            value: subject.id ? subject.id.toString() : subject.code,
            label: formatSubjectLabel(subject),
            code: subject.code,
            name: subject.fullName,
            useAssistant: true,
            assistantType: subject.assistantType,
            assistantOptions: [],
          });
        }
      } else {
        // 未启用辅助核算的科目
        options.push({
          // 新增科目的ID为null，使用code作为value；现有科目使用ID
          value: subject.id ? subject.id.toString() : subject.code,
          label: formatSubjectLabel(subject),
          code: subject.code,
          name: subject.fullName,
          useAssistant: false,
        });
      }
    }

    return options;
  });

  /**
   * 生成纯科目选项列表（不包含辅助核算信息）
   * 用于场景分录配置等不需要显示辅助核算的场景
   */
  const pureSubjectOptions = computed<AccountSubjectOption[]>(() => {
    const allSubjects = extractLeafSubjects(accountSubjects.value);
    const options: AccountSubjectOption[] = [];

    for (const subject of allSubjects) {
      // 只显示科目本身，不包含辅助核算信息
      options.push({
        // 新增科目的ID为null，使用code作为value；现有科目使用ID
        value: subject.id ? subject.id.toString() : subject.code,
        label: `${subject.code} ${subject.fullName}`, // 纯科目标签
        code: subject.code,
        name: subject.fullName,
        useAssistant: subject.useAssistant || false,
        assistantType: subject.assistantType,
        assistantOptions: [],
      });
    }

    return options;
  });

  /**
   * 检查缓存是否有效
   */
  const isCacheValid = (cacheKey: string): boolean => {
    const expiry = cacheExpiry.get(cacheKey);
    return expiry ? Date.now() < expiry : false;
  };

  /**
   * 设置缓存过期时间
   */
  const setCacheExpiry = (cacheKey: string): void => {
    cacheExpiry.set(cacheKey, Date.now() + CACHE_DURATION);
  };

  /**
   * 获取会计科目数据
   */
  const fetchAccountSubjects = async () => {
    if (!selectedCompany?.value) {
      error.value = '请先选择公司';
      return;
    }

    const cacheKey = `subjects_${selectedCompany.value}`;

    // 检查缓存
    if (isCacheValid(cacheKey) && accountSubjectsCache.has(cacheKey)) {
      accountSubjects.value = accountSubjectsCache.get(cacheKey)!;
      return;
    }

    loading.value = true;
    error.value = null;

    try {
      const params: AccountSubjectQueryParams = {
        company_name: selectedCompany.value,
      };

      // 使用新的统一API函数
      const response = await fetchAccountSubjectList(params);

      // 现在response直接是AccountSubjectResponseData类型
      if (response?.subjects) {
        const subjectsData = response.subjects;

        // 合并所有分类的科目数据
        const allSubjects: AccountSubjectItem[] = [
          ...(subjectsData.asset || []),
          ...(subjectsData.cost || []),
          ...(subjectsData.equity || []),
          ...(subjectsData.liability || []),
          ...(subjectsData.profit || []),
        ];
        // 更新数据和缓存
        accountSubjects.value = allSubjects;
        accountSubjectsCache.set(cacheKey, allSubjects);
        setCacheExpiry(cacheKey);
      } else {
        error.value = '获取会计科目数据格式错误';
      }
    } catch (err) {
      console.error('获取会计科目失败:', err);
      error.value = '获取会计科目失败';
    } finally {
      loading.value = false;
    }
  };

  /**
   * 获取辅助核算数据
   */
  const fetchAssistantAccounting = async () => {
    if (!selectedCompany?.value) {
      return;
    }

    const cacheKey = `assistant_${selectedCompany.value}`;

    // 检查缓存
    if (isCacheValid(cacheKey) && assistantAccountingCache.has(cacheKey)) {
      assistantAccounting.value = assistantAccountingCache.get(cacheKey)!;
      return;
    }

    try {
      const params: AssistantAccountingQueryParams = {
        company_name: selectedCompany.value,
      };

      // 使用新的统一API函数
      const response = await fetchAssistantAccountingList(params);

      // 现在response直接是AssistantAccountingResponseData类型
      if (response?.assistant_accounting) {
        // 更新数据和缓存
        assistantAccounting.value = response.assistant_accounting as unknown as Record<string, AssistantAccountingItem[]>;
        assistantAccountingCache.set(cacheKey, response.assistant_accounting as unknown as Record<string, AssistantAccountingItem[]>);
        setCacheExpiry(cacheKey);
      }
    } catch (err) {
      console.error('获取辅助核算数据失败:', err);
    }
  };

  /**
   * 初始化数据
   */
  const initData = async () => {
    await Promise.all([
      fetchAccountSubjects(),
      fetchAssistantAccounting(),
    ]);
  };

  /**
   * 在本地添加新科目
   */
  const addLocalSubject = (newSubject: AccountSubjectItem) => {
    accountSubjects.value.push(newSubject);
    // 可选：更新缓存
    if (selectedCompany?.value) {
      const cacheKey = `subjects_${selectedCompany.value}`;
      accountSubjectsCache.set(cacheKey, accountSubjects.value);
    }
  };

  /**
   * 在本地添加新辅助核算项
   */
  const addLocalAuxiliary = (newAuxiliary: AssistantAccountingItem) => {
    const { type } = newAuxiliary;
    if (type && assistantAccounting.value[type]) {
      assistantAccounting.value[type].push(newAuxiliary);
    } else if (type) {
      assistantAccounting.value[type] = [newAuxiliary];
    }
    // 可选：更新缓存
    if (selectedCompany?.value) {
      const cacheKey = `assistant_${selectedCompany.value}`;
      assistantAccountingCache.set(cacheKey, assistantAccounting.value);
    }
  };

  /**
   * 清除缓存
   */
  const clearCache = (companyName?: string) => {
    if (companyName) {
      // 清除指定公司的缓存
      const subjectKey = `subjects_${companyName}`;
      const assistantKey = `assistant_${companyName}`;
      accountSubjectsCache.delete(subjectKey);
      assistantAccountingCache.delete(assistantKey);
      cacheExpiry.delete(subjectKey);
      cacheExpiry.delete(assistantKey);
    } else {
      // 清除所有缓存
      accountSubjectsCache.clear();
      assistantAccountingCache.clear();
      cacheExpiry.clear();
    }
  };

  /**
   * 刷新数据（强制重新获取）
   */
  const refreshAccountSubjects = async () => {
    // 清除当前公司的缓存
    if (selectedCompany?.value) {
      clearCache(selectedCompany.value);
    }
    await initData();
  };

  // 监听公司变化，自动获取数据（安全检查）
  try {
    watch(
      () => selectedCompany?.value,
      (newCompany, oldCompany) => {
        if (newCompany && newCompany !== oldCompany) {
          initData();
        }
      },
      { immediate: true }
    );
  } catch (error) {
    console.error('监听公司变化失败:', error);
  }

  return {
    // 数据
    subjectOptions,
    pureSubjectOptions, // 纯科目选项（不包含辅助核算）
    accountSubjects,
    assistantAccounting,

    // 状态
    loading,
    error,

    // 方法
    fetchAccountSubjects,
    fetchAssistantAccounting,
    initData,
    refreshAccountSubjects,
    clearCache,
    addLocalSubject,
    addLocalAuxiliary,

    // 工具方法
    extractLeafSubjects,
    formatSubjectLabel,
    getAssistantOptions,
  };
};
